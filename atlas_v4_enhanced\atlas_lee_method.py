"""
A.T.L.<PERSON><PERSON>S Lee Method - Advanced 5-Point TTM Squeeze Pattern Detection System
Implements TTM Squeeze rebound pattern detection algorithm branded as "Lee Method"
Detects momentum reversal signals using 5-point criteria for optimal entry timing
"""

import asyncio
import logging
import json
import sys
import os
from datetime import datetime, timed<PERSON><PERSON>
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass
import pandas as pd
import numpy as np
import requests

# Add helper tools to path
sys.path.append(os.path.join(os.path.dirname(__file__), '4_helper_tools'))
try:
    from config import settings
except ImportError:
    # Fallback if config not available
    class MockSettings:
        FMP_API_KEY = "demo"
    settings = MockSettings()

logger = logging.getLogger(__name__)


# ============================================================================
# LEE METHOD DATA STRUCTURES
# ============================================================================

@dataclass
class LeeMethodSignal:
    """Lee Method signal detection result"""
    symbol: str
    signal_type: str  # 'bullish_momentum', 'bearish_momentum', 'neutral'
    entry_price: float
    target_price: float
    stop_loss: float
    confidence: float
    timeframe: str
    timestamp: datetime
    
    # Lee Method specific data
    histogram_sequence: List[float]  # The decreasing + increasing sequence
    momentum_bars: List[float]  # Momentum values for confirmation
    momentum_confirmation: bool
    
    # Multi-timeframe analysis
    weekly_trend: str  # 'bullish', 'bearish', 'neutral'
    daily_trend: str
    trend_alignment: bool  # Weekly and daily trend confirmation
    
    # Risk metrics
    risk_reward_ratio: float
    position_size_percent: float

    def to_dict(self) -> Dict[str, Any]:
        """Convert signal to dictionary for JSON serialization"""
        return {
            'symbol': self.symbol,
            'signal_type': self.signal_type,
            'entry_price': self.entry_price,
            'target_price': self.target_price,
            'stop_loss': self.stop_loss,
            'confidence': self.confidence,
            'timeframe': self.timeframe,
            'timestamp': self.timestamp.isoformat(),
            'histogram_sequence': self.histogram_sequence,
            'momentum_bars': self.momentum_bars,
            'momentum_confirmation': self.momentum_confirmation,
            'weekly_trend': self.weekly_trend,
            'daily_trend': self.daily_trend,
            'trend_alignment': self.trend_alignment,
            'risk_reward_ratio': self.risk_reward_ratio,
            'position_size_percent': self.position_size_percent
        }


# ============================================================================
# LEE METHOD SCANNER
# ============================================================================

class LeeMethodScanner:
    """Lee Method pattern scanner implementing the 5-point TTM Squeeze algorithm"""

    def __init__(self, fmp_api_key: str = None):
        self.logger = logger
        self.fmp_api_key = fmp_api_key or settings.FMP_API_KEY or "demo"
        self.base_url = "https://financialmodelingprep.com/api/v3"

        # TTM Squeeze parameters (branded as Lee Method)
        self.macd_fast = 12  # MACD fast EMA
        self.macd_slow = 26  # MACD slow EMA
        self.macd_signal = 9  # MACD signal line
        self.min_declining_bars = 3  # Exactly 3 declining histogram bars
        self.max_lookback_bars = 10   # Maximum bars to look back for pattern

        # EMA trend confirmation periods
        self.ema_periods = [5, 8, 21, 50]  # Keep existing for compatibility
        self.ema5_period = 5  # EMA 5 for trend confirmation
        self.ema8_period = 8  # EMA 8 for trend confirmation

        # TTM Squeeze parameters
        self.bb_period = 20  # Bollinger Bands period
        self.bb_std = 2.0    # Bollinger Bands standard deviation
        self.kc_period = 20  # Keltner Channels period
        self.kc_multiplier = 1.5  # Keltner Channels multiplier

        # Configurable squeeze filter options
        self.require_squeeze = False  # Optional squeeze requirement
        self.squeeze_lookback = 0     # Lookback for squeeze state (0 = current bar)

        # Risk management
        self.default_risk_percent = 2.0  # 2% risk per trade
        self.default_reward_ratio = 2.0  # 2:1 reward:risk ratio

    def configure_squeeze_filter(self, require_squeeze: bool = False, squeeze_lookback: int = 0):
        """Configure the optional TTM Squeeze filter

        Args:
            require_squeeze: Whether to require an active TTM Squeeze
            squeeze_lookback: Lookback period for squeeze state (0 = current bar)
        """
        self.require_squeeze = require_squeeze
        self.squeeze_lookback = squeeze_lookback
        self.logger.info(f"[CONFIG] TTM Squeeze filter: require={require_squeeze}, lookback={squeeze_lookback}")

    def get_ttm_squeeze_config(self) -> Dict[str, Any]:
        """Get current TTM Squeeze configuration"""
        return {
            'macd_fast': self.macd_fast,
            'macd_slow': self.macd_slow,
            'macd_signal': self.macd_signal,
            'min_declining_bars': self.min_declining_bars,
            'ema5_period': self.ema5_period,
            'ema8_period': self.ema8_period,
            'bb_period': self.bb_period,
            'bb_std': self.bb_std,
            'kc_period': self.kc_period,
            'kc_multiplier': self.kc_multiplier,
            'require_squeeze': self.require_squeeze,
            'squeeze_lookback': self.squeeze_lookback
        }

    async def fetch_historical_data(self, symbol: str, timeframe: str = "1day", limit: int = 100) -> pd.DataFrame:
        """Fetch historical data from FMP API"""
        try:
            url = f"{self.base_url}/historical-price-full/{symbol}"
            params = {
                "apikey": self.fmp_api_key,
                "timeseries": limit
            }

            # Use aiohttp for async HTTP requests
            import aiohttp
            async with aiohttp.ClientSession() as session:
                async with session.get(url, params=params, timeout=aiohttp.ClientTimeout(total=10)) as response:
                    response.raise_for_status()
                    data = await response.json()

            if 'historical' not in data:
                self.logger.warning(f"No historical data found for {symbol}")
                return pd.DataFrame()

            # Convert to DataFrame
            df = pd.DataFrame(data['historical'])
            df['date'] = pd.to_datetime(df['date'])
            df = df.sort_values('date').reset_index(drop=True)

            # Ensure we have required columns
            required_columns = ['open', 'high', 'low', 'close', 'volume']
            for col in required_columns:
                if col not in df.columns:
                    self.logger.error(f"Missing required column: {col}")
                    return pd.DataFrame()

            return df

        except Exception as e:
            self.logger.error(f"Error fetching data for {symbol}: {e}")
            return pd.DataFrame()

    def calculate_lee_method_indicators(self, df: pd.DataFrame) -> pd.DataFrame:
        """Calculate TTM Squeeze indicators (branded as Lee Method)"""
        try:
            if len(df) < max(self.macd_slow, self.bb_period, self.kc_period):
                return df

            # Calculate TTM Squeeze MACD components
            ema_fast = df['close'].ewm(span=self.macd_fast).mean()
            ema_slow = df['close'].ewm(span=self.macd_slow).mean()
            macd_line = ema_fast - ema_slow
            signal_line = macd_line.ewm(span=self.macd_signal).mean()
            histogram = macd_line - signal_line

            # Add MACD components to dataframe
            df['macd'] = macd_line
            df['signal'] = signal_line
            df['histogram'] = histogram

            # Calculate EMA 5 and EMA 8 for trend confirmation
            df['ema5'] = df['close'].ewm(span=self.ema5_period).mean()
            df['ema8'] = df['close'].ewm(span=self.ema8_period).mean()

            # Calculate all EMAs for compatibility
            for period in self.ema_periods:
                df[f'ema_{period}'] = df['close'].ewm(span=period).mean()

            # Calculate Bollinger Bands
            df['bb_middle'] = df['close'].rolling(window=self.bb_period).mean()
            bb_std = df['close'].rolling(window=self.bb_period).std()
            df['bb_upper'] = df['bb_middle'] + (bb_std * self.bb_std)
            df['bb_lower'] = df['bb_middle'] - (bb_std * self.bb_std)

            # Calculate Keltner Channels
            df['kc_middle'] = df['close'].rolling(window=self.kc_period).mean()
            # True Range calculation for Keltner Channels
            df['high_low'] = df['high'] - df['low']
            df['high_close'] = abs(df['high'] - df['close'].shift(1))
            df['low_close'] = abs(df['low'] - df['close'].shift(1))
            df['true_range'] = df[['high_low', 'high_close', 'low_close']].max(axis=1)
            atr = df['true_range'].rolling(window=self.kc_period).mean()
            df['kc_upper'] = df['kc_middle'] + (atr * self.kc_multiplier)
            df['kc_lower'] = df['kc_middle'] - (atr * self.kc_multiplier)

            # Calculate TTM Squeeze state (BB inside KC)
            df['squeeze_active'] = (df['bb_upper'] < df['kc_upper']) & (df['bb_lower'] > df['kc_lower'])

            # Clean up temporary columns
            df.drop(['high_low', 'high_close', 'low_close', 'true_range'], axis=1, inplace=True, errors='ignore')

            return df

        except Exception as e:
            self.logger.error(f"Error calculating TTM Squeeze indicators: {e}")
            return df

    def detect_lee_method_pattern(self, df: pd.DataFrame) -> Optional[Dict[str, Any]]:
        """
        Detect Lee Method pattern based on 5-point TTM Squeeze criteria:
        1. TTM Squeeze Histogram Decline Pattern: 3 consecutive declining histogram bars
        2. Histogram Rebound Signal: Less negative bounce (hist > hist[1] AND both < 0)
        3. EMA 5 Uptrend Confirmation: ema5 > ema5[1]
        4. EMA 8 Uptrend Confirmation: ema8 > ema8[1]
        5. Optional TTM Squeeze State Filter: BB inside KC (configurable)
        """
        try:
            if len(df) < self.max_lookback_bars:
                return None

            # Get recent data for pattern analysis
            recent_data = df.tail(self.max_lookback_bars)

            # Point 1: Check for exactly 3 consecutive declining histogram bars
            histogram_decline = self._check_histogram_decline_pattern(recent_data)
            if not histogram_decline['pattern_found']:
                return None

            # Point 2: Check for histogram rebound signal (less negative bounce)
            rebound_signal = self._check_histogram_rebound_signal(recent_data)
            if not rebound_signal['rebound_found']:
                return None

            # Point 3: Check EMA 5 uptrend confirmation
            ema5_uptrend = self._check_ema5_uptrend(recent_data)
            if not ema5_uptrend:
                return None

            # Point 4: Check EMA 8 uptrend confirmation
            ema8_uptrend = self._check_ema8_uptrend(recent_data)
            if not ema8_uptrend:
                return None

            # Point 5: Optional TTM Squeeze state filter
            squeeze_ok = self._check_squeeze_filter(recent_data)
            if not squeeze_ok:
                return None

            # Multi-timeframe trend analysis for compatibility
            trend_analysis = self._analyze_multi_timeframe_trends(df)

            # Determine signal direction (always bullish for TTM Squeeze rebound)
            signal_direction = 'bullish_momentum'

            # Calculate confidence based on all criteria
            confidence = self._calculate_ttm_confidence(
                histogram_decline, rebound_signal, ema5_uptrend, ema8_uptrend, squeeze_ok
            )

            return {
                'pattern_found': True,
                'histogram_sequence': histogram_decline['sequence'],
                'histogram_decline': histogram_decline,
                'rebound_signal': rebound_signal,
                'ema5_uptrend': ema5_uptrend,
                'ema8_uptrend': ema8_uptrend,
                'squeeze_active': squeeze_ok,
                'trend_analysis': trend_analysis,
                'signal_direction': signal_direction,
                'confidence': confidence,
                'entry_index': len(recent_data) - 1,  # Current bar
                'momentum_confirmation': True  # For compatibility
            }

        except Exception as e:
            self.logger.error(f"Error detecting TTM Squeeze pattern: {e}")
            return None

    def _check_histogram_decline_pattern(self, df: pd.DataFrame) -> Dict[str, Any]:
        """Check for exactly 3 consecutive declining histogram bars"""
        try:
            if len(df) < 4:  # Need at least 4 bars to check 3 declining
                return {'pattern_found': False, 'reason': 'insufficient_data'}

            # Get the last 4 histogram values to check 3 declining bars
            hist_values = df['histogram'].tail(4).values

            # Check if the last 3 bars are declining: hist[i] < hist[i-1]
            decline_1 = hist_values[-2] < hist_values[-3]  # 2nd to last < 3rd to last
            decline_2 = hist_values[-3] < hist_values[-4]  # 3rd to last < 4th to last
            decline_3 = hist_values[-1] < hist_values[-2]  # Current < previous (for context)

            # We need exactly 3 declining bars before the current bar
            three_declining = decline_1 and decline_2

            if three_declining:
                return {
                    'pattern_found': True,
                    'sequence': hist_values[-4:].tolist(),  # Last 4 values for context
                    'declining_bars': 3,
                    'decline_values': hist_values[-4:-1].tolist()  # The 3 declining bars
                }
            else:
                return {
                    'pattern_found': False,
                    'reason': 'no_three_declining_bars',
                    'sequence': hist_values.tolist()
                }

        except Exception as e:
            self.logger.error(f"Error checking histogram decline pattern: {e}")
            return {'pattern_found': False, 'reason': f'error: {e}'}

    def _check_momentum_confirmation(self, momentum_values: np.ndarray, increase_index: int) -> bool:
        """Check if momentum is greater than prior momentum bar (legacy compatibility)"""
        try:
            if increase_index < 1 or increase_index >= len(momentum_values):
                return False

            current_momentum = momentum_values[increase_index]
            prior_momentum = momentum_values[increase_index - 1]

            return current_momentum > prior_momentum

        except Exception as e:
            self.logger.error(f"Error checking momentum confirmation: {e}")
            return False

    def _check_histogram_rebound_signal(self, df: pd.DataFrame) -> Dict[str, Any]:
        """Check for histogram rebound signal: hist > hist[1] AND both < 0"""
        try:
            if len(df) < 2:
                return {'rebound_found': False, 'reason': 'insufficient_data'}

            # Get current and previous histogram values
            current_hist = df['histogram'].iloc[-1]
            previous_hist = df['histogram'].iloc[-2]

            # Check rebound conditions: hist > hist[1] AND hist < 0 AND hist[1] < 0
            hist_increased = current_hist > previous_hist
            current_negative = current_hist < 0
            previous_negative = previous_hist < 0

            rebound_signal = hist_increased and current_negative and previous_negative

            if rebound_signal:
                return {
                    'rebound_found': True,
                    'current_hist': current_hist,
                    'previous_hist': previous_hist,
                    'improvement': current_hist - previous_hist
                }
            else:
                return {
                    'rebound_found': False,
                    'reason': 'no_less_negative_bounce',
                    'current_hist': current_hist,
                    'previous_hist': previous_hist,
                    'hist_increased': hist_increased,
                    'current_negative': current_negative,
                    'previous_negative': previous_negative
                }

        except Exception as e:
            self.logger.error(f"Error checking histogram rebound signal: {e}")
            return {'rebound_found': False, 'reason': f'error: {e}'}

    def _check_ema5_uptrend(self, df: pd.DataFrame) -> bool:
        """Check if EMA 5 is trending upward: ema5 > ema5[1]"""
        try:
            if len(df) < 2:
                return False

            current_ema5 = df['ema5'].iloc[-1]
            previous_ema5 = df['ema5'].iloc[-2]

            return current_ema5 > previous_ema5

        except Exception as e:
            self.logger.error(f"Error checking EMA5 uptrend: {e}")
            return False

    def _check_ema8_uptrend(self, df: pd.DataFrame) -> bool:
        """Check if EMA 8 is trending upward: ema8 > ema8[1]"""
        try:
            if len(df) < 2:
                return False

            current_ema8 = df['ema8'].iloc[-1]
            previous_ema8 = df['ema8'].iloc[-2]

            return current_ema8 > previous_ema8

        except Exception as e:
            self.logger.error(f"Error checking EMA8 uptrend: {e}")
            return False

    def _check_squeeze_filter(self, df: pd.DataFrame) -> bool:
        """Check optional TTM Squeeze state filter"""
        try:
            if not self.require_squeeze:
                return True  # Skip squeeze filter if not required

            # Check squeeze state at specified lookback
            if len(df) <= self.squeeze_lookback:
                return False

            squeeze_index = -(self.squeeze_lookback + 1)
            squeeze_active = df['squeeze_active'].iloc[squeeze_index]

            return squeeze_active

        except Exception as e:
            self.logger.error(f"Error checking squeeze filter: {e}")
            return not self.require_squeeze  # Default to True if not required

    def _analyze_multi_timeframe_trends(self, df: pd.DataFrame) -> Dict[str, Any]:
        """Analyze multi-timeframe trends for confirmation"""
        try:
            if len(df) < 50:  # Need enough data for trend analysis
                return {
                    'daily_trend': 'neutral',
                    'weekly_trend': 'neutral',
                    'trend_alignment': False,
                    'ema_alignment': False
                }
            
            current = df.iloc[-1]
            
            # Daily trend (based on EMA alignment)
            daily_trend = 'bullish' if current['ema_5'] > current['ema_21'] else 'bearish'
            
            # Weekly trend (simplified - based on longer EMAs)
            weekly_trend = 'bullish' if current['ema_21'] > current['ema_50'] else 'bearish'
            
            # Trend alignment
            trend_alignment = (
                (daily_trend == 'bullish' and weekly_trend == 'bullish') or
                (daily_trend == 'bearish' and weekly_trend == 'bearish')
            )
            
            return {
                'daily_trend': daily_trend,
                'weekly_trend': weekly_trend,
                'trend_alignment': trend_alignment,
                'ema_alignment': current['ema_5'] > current['ema_8'] > current['ema_21']
            }
            
        except Exception as e:
            self.logger.error(f"Error analyzing multi-timeframe trends: {e}")
            return {
                'daily_trend': 'neutral',
                'weekly_trend': 'neutral',
                'trend_alignment': False,
                'ema_alignment': False
            }

    def _determine_signal_direction(self, pattern_result: Dict[str, Any], 
                                  momentum_values: np.ndarray, 
                                  trend_analysis: Dict[str, Any]) -> str:
        """Determine signal direction based on pattern and trends"""
        try:
            # Check if histogram is moving from negative to positive (bullish)
            histogram_sequence = pattern_result['sequence']
            
            if len(histogram_sequence) >= 2:
                if histogram_sequence[-1] > histogram_sequence[-2]:
                    if trend_analysis['trend_alignment'] and trend_analysis['daily_trend'] == 'bullish':
                        return 'bullish_momentum'
                    elif histogram_sequence[-1] > 0:
                        return 'bullish_momentum'
                    else:
                        return 'bearish_momentum'
            
            return 'neutral'
            
        except Exception as e:
            self.logger.error(f"Error determining signal direction: {e}")
            return 'neutral'

    def _calculate_confidence(self, pattern_result: Dict[str, Any],
                            trend_analysis: Dict[str, Any]) -> float:
        """Calculate confidence score for the signal (legacy compatibility)"""
        try:
            confidence = 0.5  # Base confidence

            # Add confidence for strong pattern
            if pattern_result.get('decrease_count', 0) >= 4:
                confidence += 0.2

            # Add confidence for trend alignment
            if trend_analysis.get('trend_alignment', False):
                confidence += 0.2

            # Add confidence for EMA alignment
            if trend_analysis.get('ema_alignment', False):
                confidence += 0.1

            return min(1.0, confidence)

        except Exception as e:
            self.logger.error(f"Error calculating confidence: {e}")
            return 0.5

    def _calculate_ttm_confidence(self, histogram_decline: Dict[str, Any], rebound_signal: Dict[str, Any],
                                ema5_uptrend: bool, ema8_uptrend: bool, squeeze_ok: bool) -> float:
        """Calculate confidence score for TTM Squeeze 5-point pattern"""
        try:
            base_confidence = 0.5

            # Point 1: Histogram decline pattern (20% weight)
            if histogram_decline.get('pattern_found', False):
                base_confidence += 0.2

            # Point 2: Rebound signal strength (25% weight)
            if rebound_signal.get('rebound_found', False):
                base_confidence += 0.25
                # Bonus for stronger rebound
                improvement = rebound_signal.get('improvement', 0)
                if improvement > 0.1:  # Significant improvement
                    base_confidence += 0.05

            # Point 3: EMA 5 uptrend (15% weight)
            if ema5_uptrend:
                base_confidence += 0.15

            # Point 4: EMA 8 uptrend (15% weight)
            if ema8_uptrend:
                base_confidence += 0.15

            # Point 5: Squeeze filter (10% weight if required)
            if self.require_squeeze:
                if squeeze_ok:
                    base_confidence += 0.1
            else:
                base_confidence += 0.05  # Small bonus for not requiring squeeze

            return min(base_confidence, 0.95)  # Cap at 95%

        except Exception as e:
            self.logger.error(f"Error calculating TTM confidence: {e}")
            return 0.5

    async def scan_symbol(self, symbol: str) -> Optional[LeeMethodSignal]:
        """Scan a single symbol for Lee Method patterns"""
        try:
            # Fetch historical data
            df = await self.fetch_historical_data(symbol, limit=100)
            if df.empty:
                return None
            
            # Calculate Lee Method indicators
            df_with_indicators = self.calculate_lee_method_indicators(df)
            
            # Detect pattern
            pattern_result = self.detect_lee_method_pattern(df_with_indicators)
            if not pattern_result or not pattern_result['pattern_found']:
                return None
            
            # Calculate entry, target, and stop prices
            current_price = df_with_indicators['close'].iloc[-1]
            entry_price = current_price
            
            # Calculate target and stop based on signal direction
            if pattern_result['signal_direction'] == 'bullish_momentum':
                target_price = entry_price * (1 + self.default_reward_ratio * self.default_risk_percent / 100)
                stop_loss = entry_price * (1 - self.default_risk_percent / 100)
            else:
                target_price = entry_price * (1 - self.default_reward_ratio * self.default_risk_percent / 100)
                stop_loss = entry_price * (1 + self.default_risk_percent / 100)
            
            # Calculate risk/reward ratio
            risk_reward_ratio = abs(target_price - entry_price) / abs(entry_price - stop_loss)
            
            # Create Lee Method signal
            signal = LeeMethodSignal(
                symbol=symbol,
                signal_type=pattern_result['signal_direction'],
                entry_price=entry_price,
                target_price=target_price,
                stop_loss=stop_loss,
                confidence=pattern_result['confidence'],
                timeframe='daily',
                timestamp=datetime.now(),
                histogram_sequence=pattern_result['histogram_sequence'],
                momentum_bars=df_with_indicators['momentum'].tail(5).tolist(),
                momentum_confirmation=pattern_result['momentum_confirmation'],
                weekly_trend=pattern_result['trend_analysis']['weekly_trend'],
                daily_trend=pattern_result['trend_analysis']['daily_trend'],
                trend_alignment=pattern_result['trend_analysis']['trend_alignment'],
                risk_reward_ratio=risk_reward_ratio,
                position_size_percent=self.default_risk_percent
            )

            return signal
            
        except Exception as e:
            self.logger.error(f"Error scanning symbol {symbol}: {e}")
            return None

    async def scan_multiple_symbols(self, symbols: List[str]) -> List[LeeMethodSignal]:
        """Scan multiple symbols for Lee Method patterns"""
        signals = []

        try:
            # Process symbols in batches to avoid overwhelming APIs
            batch_size = 5
            for i in range(0, len(symbols), batch_size):
                batch = symbols[i:i + batch_size]

                # Create tasks for concurrent processing
                tasks = [self.scan_symbol(symbol) for symbol in batch]
                batch_results = await asyncio.gather(*tasks, return_exceptions=True)

                # Process results
                for result in batch_results:
                    if isinstance(result, LeeMethodSignal):
                        signals.append(result)
                    elif isinstance(result, Exception):
                        self.logger.error(f"Error in batch processing: {result}")

                # Small delay between batches
                await asyncio.sleep(0.5)

            return signals

        except Exception as e:
            self.logger.error(f"Error scanning multiple symbols: {e}")
            return signals


# ============================================================================
# REAL-TIME SCANNER
# ============================================================================

class AtlasLeeMethodRealtimeScanner:
    """Real-time Lee Method scanner for A.T.L.A.S. system"""
    
    def __init__(self, fmp_api_key: str = None):
        self.logger = logger
        self.lee_scanner = LeeMethodScanner(fmp_api_key)
        
        # Scanner configuration
        self.scan_symbols = [
            "AAPL", "MSFT", "GOOGL", "AMZN", "TSLA", "META", "NVDA", "NFLX",
            "AMD", "INTC", "CRM", "ORCL", "ADBE", "PYPL", "UBER", "LYFT",
            "SHOP", "SQ", "ROKU", "ZM", "DOCU", "SNOW", "PLTR", "COIN"
        ]
        
        # Scanner state
        self.is_running = False
        self.scan_interval = 30  # seconds
        self.last_scan_time = None
        self.scan_count = 0
        self.active_signals = {}
        self.scan_task = None
        
        # Performance tracking
        self.scan_times = []
        self.error_count = 0

    async def start_scanning(self):
        """Start the real-time scanning process"""
        if self.is_running:
            self.logger.warning("Scanner is already running")
            return
        
        self.is_running = True
        self.logger.info("[SCANNER] Starting Lee Method real-time scanner")
        
        # Start scanning task
        self.scan_task = asyncio.create_task(self._scanning_loop())

    async def stop_scanning(self):
        """Stop the real-time scanning process"""
        if not self.is_running:
            return
        
        self.is_running = False
        
        if self.scan_task:
            self.scan_task.cancel()
            try:
                await self.scan_task
            except asyncio.CancelledError:
                pass
        
        self.logger.info("[SCANNER] Lee Method scanner stopped")

    async def _scanning_loop(self):
        """Main scanning loop"""
        while self.is_running:
            try:
                await self._perform_lee_method_scan()
                await asyncio.sleep(self.scan_interval)
            except asyncio.CancelledError:
                break
            except Exception as e:
                self.logger.error(f"Error in scanning loop: {e}")
                self.error_count += 1
                await asyncio.sleep(5)  # Brief pause on error

    async def _perform_lee_method_scan(self):
        """Perform Lee Method scan on all symbols"""
        try:
            scan_start = datetime.now()
            self.scan_count += 1
            self.last_scan_time = scan_start
            
            # Clear old signals (older than 1 hour)
            self._cleanup_old_signals()
            
            # Scan symbols in batches to avoid overwhelming APIs
            batch_size = 5
            new_signals = []
            
            for i in range(0, len(self.scan_symbols), batch_size):
                batch = self.scan_symbols[i:i + batch_size]
                batch_results = await self._scan_batch(batch)
                new_signals.extend(batch_results)
                
                # Small delay between batches
                await asyncio.sleep(0.5)
            
            # Update active signals
            for signal in new_signals:
                self.active_signals[signal.symbol] = signal
            
            # Track performance
            scan_duration = (datetime.now() - scan_start).total_seconds()
            self.scan_times.append(scan_duration)
            
            # Keep only recent scan times (last 100)
            if len(self.scan_times) > 100:
                self.scan_times = self.scan_times[-100:]
            
            self.logger.info(f"[SCANNER] Scan #{self.scan_count} completed in {scan_duration:.2f}s - {len(new_signals)} new signals")
            
        except Exception as e:
            self.logger.error(f"Error performing Lee Method scan: {e}")
            self.error_count += 1

    async def _scan_batch(self, symbols: List[str]) -> List[LeeMethodSignal]:
        """Scan a batch of symbols"""
        signals = []
        
        try:
            tasks = [self.lee_scanner.scan_symbol(symbol) for symbol in symbols]
            results = await asyncio.gather(*tasks, return_exceptions=True)
            
            for result in results:
                if isinstance(result, LeeMethodSignal):
                    signals.append(result)
                elif isinstance(result, Exception):
                    self.logger.error(f"Error in batch scan: {result}")
            
        except Exception as e:
            self.logger.error(f"Error scanning batch {symbols}: {e}")
        
        return signals

    def _cleanup_old_signals(self):
        """Remove signals older than 1 hour"""
        cutoff_time = datetime.now() - timedelta(hours=1)
        
        symbols_to_remove = []
        for symbol, signal in self.active_signals.items():
            if signal.timestamp < cutoff_time:
                symbols_to_remove.append(symbol)
        
        for symbol in symbols_to_remove:
            del self.active_signals[symbol]

    def get_latest_signals(self, limit: int = 10) -> List[Dict[str, Any]]:
        """Get the latest Lee Method signals"""
        signals = list(self.active_signals.values())
        signals.sort(key=lambda x: x.timestamp, reverse=True)
        
        return [signal.to_dict() for signal in signals[:limit]]

    def get_scanner_status(self) -> Dict[str, Any]:
        """Get scanner status information"""
        avg_scan_time = sum(self.scan_times) / len(self.scan_times) if self.scan_times else 0
        
        return {
            'is_running': self.is_running,
            'scan_count': self.scan_count,
            'last_scan_time': self.last_scan_time.isoformat() if self.last_scan_time else None,
            'active_signals_count': len(self.active_signals),
            'symbols_monitored': len(self.scan_symbols),
            'error_count': self.error_count,
            'average_scan_time': round(avg_scan_time, 2),
            'scan_interval': self.scan_interval
        }


# ============================================================================
# SCANNER INSTANCE MANAGEMENT
# ============================================================================

_scanner_instance = None

def get_scanner_instance(fmp_api_key: str = None) -> AtlasLeeMethodRealtimeScanner:
    """Get or create scanner instance"""
    global _scanner_instance
    
    if _scanner_instance is None:
        _scanner_instance = AtlasLeeMethodRealtimeScanner(fmp_api_key)
    
    return _scanner_instance


# ============================================================================
# LEE METHOD CRITERIA INFORMATION
# ============================================================================

def get_lee_method_criteria() -> Dict[str, Any]:
    """Get Lee Method criteria information (5-point TTM Squeeze algorithm)"""
    return {
        'name': 'Lee Method',
        'description': 'Advanced 5-point TTM Squeeze pattern detection for momentum reversal signals',
        'algorithm': 'TTM Squeeze Rebound Pattern',
        'criteria': [
            {
                'number': 1,
                'name': 'TTM Squeeze Histogram Decline Pattern',
                'description': 'Exactly 3 consecutive declining histogram bars',
                'detail': 'Detects hist < hist[1] for 3 consecutive bars, indicating momentum decline'
            },
            {
                'number': 2,
                'name': 'Histogram Rebound Signal',
                'description': 'Less negative bounce where histogram improves but remains negative',
                'detail': 'Current histogram > previous histogram AND both values < 0 (anticipates first yellow bar after 3 red bars)'
            },
            {
                'number': 3,
                'name': 'EMA 5 Uptrend Confirmation',
                'description': '5-period exponential moving average trending upward',
                'detail': 'EMA5 > EMA5[1] confirms short-term upward price momentum'
            },
            {
                'number': 4,
                'name': 'EMA 8 Uptrend Confirmation',
                'description': '8-period exponential moving average trending upward',
                'detail': 'EMA8 > EMA8[1] confirms medium-term upward price momentum'
            },
            {
                'number': 5,
                'name': 'Optional TTM Squeeze State Filter',
                'description': 'Configurable requirement for active TTM Squeeze (BB inside KC)',
                'detail': 'Optional filter requiring Bollinger Bands inside Keltner Channels with configurable lookback'
            }
        ],
        'technical_specs': {
            'macd_settings': 'MACD(12,26,9)',
            'bollinger_bands': 'BB(20,2)',
            'keltner_channels': 'KC(20,1.5)',
            'ema_periods': [5, 8],
            'squeeze_filter': 'Optional (configurable)'
        },
        'advantages': [
            'Anticipates momentum reversal at optimal entry point',
            'Detects first less-negative histogram bar after decline',
            'Dual EMA confirmation reduces false signals',
            'Optional squeeze filter for high-probability setups',
            'More precise timing than traditional TTM Squeeze'
        ],
        'signal_type': 'Bullish momentum reversal anticipation'
    }


# ============================================================================
# EXPORTS
# ============================================================================

__all__ = [
    "LeeMethodScanner",
    "AtlasLeeMethodRealtimeScanner",
    "LeeMethodSignal",
    "get_scanner_instance",
    "get_lee_method_criteria"
]
